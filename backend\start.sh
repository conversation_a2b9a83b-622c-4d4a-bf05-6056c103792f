#!/bin/bash
set -e

echo "Starting production deployment..."

# Set required environment variables if not set
export FLASK_ENV=production

# Debug: Test imports
echo "Running import test..."
python test_import.py

# Check if required environment variables are set
echo "Checking environment variables..."
if [ -z "$DATABASE_URL" ]; then
    echo "ERROR: DATABASE_URL is not set"
    exit 1
fi

if [ -z "$SECRET_KEY" ]; then
    echo "ERROR: SECRET_KEY is not set"
    exit 1
fi

# Initialize the database
echo "Initializing production database..."
python init_production_db.py

# Start the application
echo "Starting Flask application with Gunicorn..."
exec gunicorn --bind 0.0.0.0:$PORT --workers 2 --timeout 120 --access-logfile - --error-logfile - app:app
